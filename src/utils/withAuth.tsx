import { useUserInfoQuery } from "@/redux/features/auth/auth.api";
import { Navigate } from "react-router";
import type { TRole } from "../types";

export const withAuth = (
  Component: React.ComponentType,
  requiredRole?: TRole
) => {
  return function AuthWrapper() {
    const { data: user, isLoading } = useUserInfoQuery();

    if (!isLoading && !user?.data?.email) {
      return <Navigate to="/login" />;
    }
    const userRole = user?.data?.role;
    if (!isLoading && requiredRole && userRole !== requiredRole) {
      return <Navigate to="/unauthorized" replace />;
    }
    return <Component />;
  };
};
